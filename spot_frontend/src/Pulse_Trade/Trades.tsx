import React, { useState, useEffect } from 'react';
import { ChevronUp, ChevronDown, DollarSign, RefreshCcw,ArrowLeftRight,Filter,User} from 'lucide-react';
import { useTradeData } from '@/hooks/useTradeData';

const Trades = () => {
  const [activeTab, setActiveTab] = useState('TRADES');
  const [sortAsc, setSortAsc] = useState(true);
  const [showPrice, setShowPrice] = useState(false);
  const [showUSD, setShowUSD] = useState(false);
  const [poolAddress, setPoolAddress] = useState<string | null>(null);

  // Get trade data using the custom hook
  const { trades, isLoading, isConnected, error, lastUpdate } = useTradeData(poolAddress);

  // Debug trade data
  useEffect(() => {
    console.log('📊 Trades: Hook data update:', {
      poolAddress,
      tradesCount: trades.length,
      isLoading,
      isConnected,
      error,
      lastUpdate: lastUpdate ? new Date(lastUpdate).toLocaleTimeString() : null
    });
  }, [poolAddress, trades, isLoading, isConnected, error, lastUpdate]);

  // Get pool address from localStorage on component mount
  useEffect(() => {
    const activePulseToken = localStorage.getItem('activePulseToken');
    if (activePulseToken) {
      try {
        const tokenData = JSON.parse(activePulseToken);
        console.log('📊 Trades: Token data from localStorage:', tokenData);

        // Try multiple fields for pool address
        const poolAddr = tokenData.pool_address ||
                         tokenData.address ||
                         tokenData.id ||
                         tokenData.contract ||
                         tokenData.pair_address;

        if (poolAddr) {
          setPoolAddress(poolAddr);
          console.log('📊 Trades: Using pool address:', poolAddr);
          console.log('📊 Trades: Token symbol:', tokenData.symbol);
          console.log('📊 Trades: Token name:', tokenData.name);
        } else {
          console.warn('📊 Trades: No pool address found in token data:', Object.keys(tokenData));
          // Use a default test pool address for development
          const testPoolAddress = '3Sh7S9XwatY5aUCuufpzGrjT4PiqzMS6psuBpDcXKXXe';
          setPoolAddress(testPoolAddress);
          console.log('📊 Trades: Using test pool address:', testPoolAddress);
        }
      } catch (error) {
        console.error('📊 Trades: Failed to parse activePulseToken:', error);
        // Use a default test pool address for development
        const testPoolAddress = '3Sh7S9XwatY5aUCuufpzGrjT4PiqzMS6psuBpDcXKXXe';
        setPoolAddress(testPoolAddress);
        console.log('📊 Trades: Using test pool address after error:', testPoolAddress);
      }
    } else {
      console.warn('📊 Trades: No activePulseToken found in localStorage');
      // Use a default test pool address for development
      const testPoolAddress = '3Sh7S9XwatY5aUCuufpzGrjT4PiqzMS6psuBpDcXKXXe';
      setPoolAddress(testPoolAddress);
      console.log('📊 Trades: Using test pool address (no localStorage):', testPoolAddress);
    }
  }, []);

  const devData = [
    { amount: '45.2', usdAmount: '$765.4', mc: '$12.3K', price: '$0.18', trader: 'Dev1', age: '1h', type: 'buy' },
  ];

  const youData = [
    { amount: '7.8', usdAmount: '$132.6', mc: '$5.67K', price: '$0.12', trader: 'YOU', age: '3h', type: 'buy' },
  ];

  const getCurrentData = () => {
    let data;

    if (activeTab === 'DEV') {
      data = devData;
    } else if (activeTab === 'YOU') {
      data = youData;
    } else {
      // Use real trade data for TRADES tab
      data = trades.map(trade => ({
        amount: trade.amount,
        usdAmount: trade.usdAmount,
        mc: trade.mc,
        price: trade.price,
        trader: trade.trader,
        age: trade.age,
        type: trade.type
      }));
    }

    return [...data].sort((a, b) => {
      const aAge = parseInt(a.age.replace(/[hms]/g, ''));
      const bAge = parseInt(b.age.replace(/[hms]/g, ''));
      return sortAsc ? aAge - bAge : bAge - aAge;
    });
  };

  return (
    <div className="text-white max-h-[100rem] font-mono text-sm ">
      {/* Header Tabs */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
        {/* Connection Status Indicator */}
        {activeTab === 'TRADES' && (
          <div className="flex items-center space-x-2 text-xs">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
            <span className="text-gray-400">
              {isLoading ? 'Loading...' : isConnected ? `Live • ${trades.length} trades` : 'Disconnected'}
            </span>
            {error && <span className="text-red-400">Error: {error}</span>}
          </div>
        )}
  {/* Left side: TRADES */}
  <div>
    <button
      onClick={() => setActiveTab('TRADES')}
      className={`text-lg font-bold tracking-wide transition-colors ${
        activeTab === 'TRADES' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
      }`}
    >
      Trades
    </button>
  </div>

  {/* Right side: DEV and YOU */}
  <div className="flex space-x-6">
    <button
      onClick={() => setActiveTab('DEV')}
      className={`flex items-center text-lg font-bold tracking-wide transition-colors ${
        activeTab === 'DEV' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
      }`}
    >
      <Filter size={18} className="mr-1" />
      DEV
    </button>

    <button
      onClick={() => setActiveTab('YOU')}
      className={`flex items-center text-lg font-bold tracking-wide transition-colors ${
        activeTab === 'YOU' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
      }`}
    >
      <User size={18} className="mr-1" />
      YOU
    </button>
  </div>
</div>

      {/* Column Headers */}
      <div className="flex items-center px-4 py-3 text-gray-400 text-xs border-b border-gray-800">
        {/* Amount / USD Toggle */}
        <div className="flex-1 flex items-center space-x-2">
          <span>Amount</span>
          <button
            onClick={() => setShowUSD(!showUSD)}
            className={`w-5 h-5 flex items-center justify-center rounded-full border transition-transform ${
              showUSD ? 'border-green-500 text-green-400' : 'border-gray-500 text-gray-400'
            } hover:scale-110`}
            title="Toggle USD/SOL"
          >
            <DollarSign size={10} />
          </button>
        </div>

        {/* Market Cap / Price Toggle */}
        <div className="w-24 flex items-center space-x-1 pl-4">
          <span>{showPrice ? 'Price' : 'MC'}</span>
          <button
  onClick={() => setShowPrice(!showPrice)}
  className="hover:text-white transition-colors"
  title="Toggle MC / Price"
>
  <ArrowLeftRight size={14} />
</button>

        </div>

        {/* Trader */}
        <div className="w-20 text-center">Trader</div>

        {/* Age Sort */}
        <div
          className="w-16 flex items-center justify-end cursor-pointer"
          onClick={() => setSortAsc(!sortAsc)}
        >
          <span>Age</span>
          {sortAsc ? <ChevronUp size={12} className="ml-1" /> : <ChevronDown size={12} className="ml-1" />}
        </div>
      </div>

      {/* Trade Entries */}
      <div className="divide-y divide-gray-800">
        {activeTab === 'TRADES' && isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="text-gray-400">Loading trade data...</div>
          </div>
        )}

        {activeTab === 'TRADES' && !isLoading && getCurrentData().length === 0 && (
          <div className="flex items-center justify-center py-8">
            <div className="text-gray-400">
              {error ? 'Failed to load trade data' : 'No trades available'}
            </div>
          </div>
        )}

        {getCurrentData().map((trade, index) => (
          <div key={index} className="flex items-center px-4 py-2 hover:bg-gray-800/50 transition-colors">
            <div
              className={`flex-1 font-medium flex items-center space-x-1  ${
                trade.type === 'buy' ? 'text-green-400' : 'text-red-400'
              }`}
            >
              {!showUSD && (
                <img
                  src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
                  alt="sol"
                  className="w-3 h-3"
                />
              )}
              <span>{showUSD ? trade.usdAmount : trade.amount}</span>
            </div>
            <div className="w-24 text-gray-300 font-medium pl-4">{showPrice ? trade.price : trade.mc}</div>
            <div className="w-20 text-center text-gray-300 font-medium">{trade.trader}</div>
            <div className="w-16 text-right text-gray-400">{trade.age}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Trades;
